import React from 'react';

interface InsightsScreenProps {
  // Add any props you need here
}

interface InsightCard {
  id: number;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  date: string;
  image?: string;
}

const InsightsScreen: React.FC<InsightsScreenProps> = () => {
  const insights: InsightCard[] = [
    {
      id: 1,
      title: "The Future of Digital Transformation",
      excerpt: "Explore how emerging technologies are reshaping business landscapes and what it means for your organization.",
      category: "Technology",
      readTime: "5 min read",
      date: "2024-01-15"
    },
    {
      id: 2,
      title: "Customer Experience in the Digital Age",
      excerpt: "Understanding how to create meaningful connections with customers through digital touchpoints.",
      category: "Customer Experience",
      readTime: "7 min read",
      date: "2024-01-10"
    },
    {
      id: 3,
      title: "Data-Driven Decision Making",
      excerpt: "Learn how to leverage analytics and insights to make informed business decisions that drive growth.",
      category: "Analytics",
      readTime: "6 min read",
      date: "2024-01-05"
    },
    {
      id: 4,
      title: "Building Resilient Business Models",
      excerpt: "Strategies for creating adaptable business models that can withstand market uncertainties.",
      category: "Strategy",
      readTime: "8 min read",
      date: "2023-12-28"
    },
    {
      id: 5,
      title: "The Role of AI in Modern Business",
      excerpt: "Discover practical applications of artificial intelligence and how it can transform your operations.",
      category: "Artificial Intelligence",
      readTime: "10 min read",
      date: "2023-12-20"
    },
    {
      id: 6,
      title: "Sustainable Business Practices",
      excerpt: "How to integrate sustainability into your business strategy while maintaining profitability.",
      category: "Sustainability",
      readTime: "6 min read",
      date: "2023-12-15"
    }
  ];

  const categories = ["All", "Technology", "Customer Experience", "Analytics", "Strategy", "Artificial Intelligence", "Sustainability"];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">Insights & Perspectives</h1>
        
        <p className="text-lg text-gray-600 text-center mb-12 max-w-3xl mx-auto">
          Stay ahead of the curve with our latest insights, industry trends, and expert perspectives
          on topics that matter to your business.
        </p>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 rounded-full border border-gray-300 hover:border-blue-500 hover:text-blue-500 transition-colors"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Featured Insight */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white mb-12">
          <div className="max-w-3xl">
            <span className="inline-block bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm mb-4">
              Featured
            </span>
            <h2 className="text-3xl font-bold mb-4">{insights[0].title}</h2>
            <p className="text-lg mb-6 opacity-90">{insights[0].excerpt}</p>
            <div className="flex items-center gap-4 text-sm opacity-80">
              <span>{insights[0].category}</span>
              <span>•</span>
              <span>{insights[0].readTime}</span>
              <span>•</span>
              <span>{new Date(insights[0].date).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Insights Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {insights.slice(1).map((insight) => (
            <article key={insight.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-48 bg-gray-200"></div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                    {insight.category}
                  </span>
                  <span className="text-xs text-gray-500">{insight.readTime}</span>
                </div>
                <h3 className="text-xl font-semibold mb-3 hover:text-blue-600 cursor-pointer">
                  {insight.title}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {insight.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {new Date(insight.date).toLocaleDateString()}
                  </span>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                    Read More →
                  </button>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="bg-gray-50 rounded-lg p-8 mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">Stay Updated</h2>
          <p className="text-gray-600 mb-6">
            Subscribe to our newsletter to receive the latest insights and updates directly in your inbox.
          </p>
          <div className="flex max-w-md mx-auto gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
            />
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightsScreen;
