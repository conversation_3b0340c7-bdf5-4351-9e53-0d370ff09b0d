import React from "react";

interface InsightsScreenProps {
  // Add any props you need here
}

interface InsightCard {
  id: number;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  date: string;
}

const InsightsScreen: React.FC<InsightsScreenProps> = () => {
  const insights: InsightCard[] = [
    {
      id: 1,
      title: "Tax-Efficient Investing in Dubai",
      excerpt: "Explore the various tax advantages and investment opportunities available in Dubai's thriving financial landscape.",
      category: "Tax Planning",
      readTime: "5 min read",
      date: "2024-04-18"
    },
    {
      id: 2,
      title: "The Benefits of Trusts in Dubai",
      excerpt: "Understanding how trust structures can protect your assets and provide tax efficiency in the UAE.",
      category: "Trust Formation",
      readTime: "7 min read",
      date: "2024-03-28"
    },
    {
      id: 3,
      title: "Why Entrepreneurs Are Moving to Dubai",
      excerpt: "Discover the business-friendly environment and opportunities that make Dubai attractive to global entrepreneurs.",
      category: "Business Setup",
      readTime: "6 min read",
      date: "2024-02-10"
    },
    {
      id: 4,
      title: "International Tax Compliance",
      excerpt: "Navigate the complex world of international tax regulations and ensure full compliance.",
      category: "Tax Advisory",
      readTime: "8 min read",
      date: "2024-01-15"
    },
    {
      id: 5,
      title: "Wealth Preservation Strategies",
      excerpt: "Learn effective strategies to preserve and grow your wealth across different jurisdictions.",
      category: "Wealth Management",
      readTime: "10 min read",
      date: "2023-12-20"
    },
    {
      id: 6,
      title: "Dubai Free Zone Benefits",
      excerpt: "Explore the advantages of setting up your business in Dubai's various free zones.",
      category: "Business Setup",
      readTime: "6 min read",
      date: "2023-11-30"
    }
  ];

  const categories = ["All", "Tax Planning", "Trust Formation", "Business Setup", "Tax Advisory", "Wealth Management"];

  return (
    <div className="bg-white min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8 text-gray-900">Insights & Perspectives</h1>
          
          <p className="text-lg text-gray-700 text-center mb-12 max-w-3xl mx-auto">
            Stay ahead of the curve with our latest insights, industry trends, and expert perspectives
            on topics that matter to your business and wealth management.
          </p>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-full border border-gray-300 bg-white hover:border-blue-500 hover:text-blue-500 hover:bg-blue-50 transition-colors text-gray-700"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Featured Insight */}
          <div className="bg-blue-600 rounded-lg p-8 text-white mb-12 border border-blue-700">
            <div className="max-w-3xl">
              <span className="inline-block bg-white bg-opacity-90 text-blue-600 px-3 py-1 rounded-full text-sm mb-4 font-medium">
                Featured
              </span>
              <h2 className="text-3xl font-bold mb-4">{insights[0].title}</h2>
              <p className="text-lg mb-6 text-blue-50">{insights[0].excerpt}</p>
              <div className="flex items-center gap-4 text-sm text-blue-100">
                <span>{insights[0].category}</span>
                <span>•</span>
                <span>{insights[0].readTime}</span>
                <span>•</span>
                <span>{new Date(insights[0].date).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Insights Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {insights.slice(1).map((insight) => (
              <article key={insight.id} className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden hover:border-gray-300 hover:shadow-sm transition-all">
                <div className="h-48 bg-gray-300"></div>
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
                      {insight.category}
                    </span>
                    <span className="text-xs text-gray-600">{insight.readTime}</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 hover:text-blue-600 cursor-pointer text-gray-900">
                    {insight.title}
                  </h3>
                  <p className="text-gray-700 mb-4 line-clamp-3">
                    {insight.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      {new Date(insight.date).toLocaleDateString()}
                    </span>
                    <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                      Read More →
                    </button>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Newsletter Signup */}
          <div className="bg-gray-100 rounded-lg p-8 mt-16 text-center border border-gray-200">
            <h2 className="text-2xl font-bold mb-4 text-gray-900">Stay Updated</h2>
            <p className="text-gray-700 mb-6">
              Subscribe to our newsletter to receive the latest insights and updates directly in your inbox.
            </p>
            <div className="flex max-w-md mx-auto gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 bg-white"
              />
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightsScreen;
