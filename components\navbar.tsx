"use client";
import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NavbarContent,
  NavbarMenu,
  NavbarMenuToggle,
  NavbarBrand,
  NavbarItem,
  NavbarMenuItem,
} from "@heroui/navbar";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import NextLink from "next/link";
import { useState } from "react";

import { siteConfig } from "@/config/site";

export const Navbar = () => {
  const [language, setLanguage] = useState("EN");

  return (
    <HeroUINavbar
      maxWidth="xl"
      position="sticky"
      className="bg-white/95 backdrop-blur-md border-b border-gray-200"
      classNames={{
        wrapper: "px-4 sm:px-6 lg:px-8",
      }}
    >
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink className="flex justify-start items-center gap-2" href="/">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-lg">P</span>
            </div>
            <div className="flex flex-col">
              <p className="font-bold text-gray-900 text-sm leading-tight">
                Proactive
              </p>
              <p className="font-bold text-gray-900 text-sm leading-tight">
                Wealth Management
              </p>
            </div>
          </NextLink>
        </NavbarBrand>
        <ul className="hidden lg:flex gap-8 justify-start ml-8">
          {siteConfig.navItems.map((item) => (
            <NavbarItem key={item.href}>
              <NextLink
                className="text-gray-700 hover:text-blue-600 font-medium text-sm transition-colors"
                href={item.href}
              >
                {item.label}
              </NextLink>
            </NavbarItem>
          ))}
        </ul>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <NavbarItem className="flex gap-4 items-center">
          <div className="flex gap-2 text-sm">
            <button
              className={`px-2 py-1 ${language === "EN" ? "text-blue-600 font-semibold" : "text-gray-600"}`}
              onClick={() => setLanguage("EN")}
            >
              EN
            </button>
            <span className="text-gray-400">|</span>
            <button
              className={`px-2 py-1 ${language === "AR" ? "text-blue-600 font-semibold" : "text-gray-600"}`}
              onClick={() => setLanguage("AR")}
            >
              AR
            </button>
          </div>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
        <div className="flex gap-2 text-sm mr-2">
          <button
            className={`px-2 py-1 ${language === "EN" ? "text-blue-600 font-semibold" : "text-gray-600"}`}
            onClick={() => setLanguage("EN")}
          >
            EN
          </button>
          <span className="text-gray-400">|</span>
          <button
            className={`px-2 py-1 ${language === "AR" ? "text-blue-600 font-semibold" : "text-gray-600"}`}
            onClick={() => setLanguage("AR")}
          >
            AR
          </button>
        </div>
        <NavbarMenuToggle />
      </NavbarContent>

      <NavbarMenu className="bg-white">
        <div className="mx-4 mt-2 flex flex-col gap-2">
          {siteConfig.navMenuItems.map((item, index) => (
            <NavbarMenuItem key={`${item.label}-${index}`}>
              <NextLink
                className="text-gray-700 hover:text-blue-600 font-medium text-lg py-2"
                href={item.href}
              >
                {item.label}
              </NextLink>
            </NavbarMenuItem>
          ))}
        </div>
      </NavbarMenu>
    </HeroUINavbar>
  );
};
