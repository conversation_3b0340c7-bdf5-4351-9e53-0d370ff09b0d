"use client";
import React from "react";

interface ClientJourneyScreenProps {
  // Add any props you need here
}

const ClientJourneyScreen: React.FC<ClientJourneyScreenProps> = () => {
  const journeySteps = [
    {
      step: 1,
      title: "Consultation",
      description:
        "We analyze your needs and develop a strategic plan tailored to your specific requirements.",
      icon: "🔍",
    },
    {
      step: 2,
      title: "Tax Structure",
      description:
        "We design the optimal, tax-efficient structure for your unique situation.",
      icon: "📋",
    },
    {
      step: 3,
      title: "Legal Setup",
      description:
        "Our experts handle all legal and financial requirements with precision.",
      icon: "⚡",
    },
    {
      step: 4,
      title: "Ongoing Advisory",
      description:
        "We continue to optimize and ensure long-term success for your wealth management.",
      icon: "🤝",
    },
  ];

  return (
    <div className="bg-white min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8 text-gray-900">
            Client Journey
          </h1>

          <p className="text-lg text-gray-700 text-center mb-12 max-w-3xl mx-auto">
            Discover how we guide our clients from initial consultation to
            successful project completion. Our proven process ensures
            transparency, quality, and exceptional results at every step.
          </p>

          {/* Journey Timeline */}
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gray-300 h-full hidden md:block"></div>

            {journeySteps.map((item, index) => (
              <div
                key={index}
                className={`flex items-center mb-12 ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"}`}
              >
                {/* Content */}
                <div
                  className={`w-full md:w-5/12 ${index % 2 === 0 ? "md:pr-8" : "md:pl-8"}`}
                >
                  <div className="bg-gray-50 p-6 rounded-lg border border-gray-200 border-l-4 border-l-primary">
                    <div className="flex items-center mb-4">
                      <span className="text-3xl mr-4">{item.icon}</span>
                      <div>
                        <span className="text-sm text-primary font-semibold">
                          Step {item.step}
                        </span>
                        <h3 className="text-xl font-bold text-gray-900">
                          {item.title}
                        </h3>
                      </div>
                    </div>
                    <p className="text-gray-700">{item.description}</p>
                  </div>
                </div>

                {/* Timeline dot */}
                <div className="hidden md:flex w-2/12 justify-center">
                  <div className="w-4 h-4 bg-primary rounded-full border-4 border-white shadow-sm z-10"></div>
                </div>

                {/* Empty space for alternating layout */}
                <div className="hidden md:block w-5/12"></div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16 bg-gray-50 p-8 rounded-lg border border-gray-200">
            <h2 className="text-2xl font-bold mb-4 text-gray-900">
              Ready to Start Your Journey?
            </h2>
            <p className="text-gray-700 mb-8">
              Let's discuss how we can help you achieve your goals with our
              proven process.
            </p>
            <button className="bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary/90 transition-colors shadow-sm">
              Get Started Today
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientJourneyScreen;
