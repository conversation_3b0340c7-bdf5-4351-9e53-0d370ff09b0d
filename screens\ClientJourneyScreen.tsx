import React from 'react';

interface ClientJourneyScreenProps {
  // Add any props you need here
}

const ClientJourneyScreen: React.FC<ClientJourneyScreenProps> = () => {
  const journeySteps = [
    {
      step: 1,
      title: "Discovery",
      description: "We start by understanding your needs, goals, and challenges through detailed consultation.",
      icon: "🔍"
    },
    {
      step: 2,
      title: "Planning",
      description: "Our team creates a comprehensive strategy tailored to your specific requirements.",
      icon: "📋"
    },
    {
      step: 3,
      title: "Implementation",
      description: "We execute the plan with precision, keeping you informed throughout the process.",
      icon: "⚡"
    },
    {
      step: 4,
      title: "Delivery",
      description: "Your solution is delivered on time, meeting all quality standards and expectations.",
      icon: "🚀"
    },
    {
      step: 5,
      title: "Support",
      description: "We provide ongoing support and maintenance to ensure continued success.",
      icon: "🤝"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">Client Journey</h1>
        
        <p className="text-lg text-gray-600 text-center mb-12 max-w-3xl mx-auto">
          Discover how we guide our clients from initial consultation to successful project completion.
          Our proven process ensures transparency, quality, and exceptional results at every step.
        </p>

        {/* Journey Timeline */}
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-blue-200 h-full hidden md:block"></div>
          
          {journeySteps.map((item, index) => (
            <div key={index} className={`flex items-center mb-12 ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
              {/* Content */}
              <div className={`w-full md:w-5/12 ${index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'}`}>
                <div className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-blue-500">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-4">{item.icon}</span>
                    <div>
                      <span className="text-sm text-blue-600 font-semibold">Step {item.step}</span>
                      <h3 className="text-xl font-bold text-gray-800">{item.title}</h3>
                    </div>
                  </div>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              </div>
              
              {/* Timeline dot */}
              <div className="hidden md:flex w-2/12 justify-center">
                <div className="w-4 h-4 bg-blue-500 rounded-full border-4 border-white shadow-lg z-10"></div>
              </div>
              
              {/* Empty space for alternating layout */}
              <div className="hidden md:block w-5/12"></div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <h2 className="text-2xl font-bold mb-4">Ready to Start Your Journey?</h2>
          <p className="text-gray-600 mb-8">
            Let's discuss how we can help you achieve your goals with our proven process.
          </p>
          <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            Get Started Today
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClientJourneyScreen;
