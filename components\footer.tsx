import { Link } from "@heroui/link";
import { siteConfig } from "@/config/site";

export const Footer = () => {
  return (
    <footer className="bg-primary-900 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg">
                <span className="text-primary font-bold text-lg">P</span>
              </div>
              <div className="flex flex-col">
                <p className="font-bold text-white text-sm leading-tight">
                  Proactive
                </p>
                <p className="font-bold text-white text-sm leading-tight">
                  Wealth Management
                </p>
              </div>
            </div>
            <p className="text-slate-300 mb-6 max-w-md leading-relaxed">
              Your legal edge in global tax & trust planning. We help you
              preserve more, pay less, and protect everything.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-white mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  Our Services
                </Link>
              </li>
              <li>
                <Link
                  href="/client-journey"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  Client Journey
                </Link>
              </li>
              <li>
                <Link
                  href="/insights"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  Insights
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  Book a Consultation
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-white mb-4">Contact Us</h3>
            <div className="space-y-2 text-slate-300">
              <p>{siteConfig.contact.address}</p>
              <p>Phone: {siteConfig.contact.phone}</p>
              <p>Email: {siteConfig.contact.email}</p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <p className="text-slate-400 text-sm">
                © 2024 Proactive Wealth Management. All Rights Reserved.
              </p>
            </div>
            <div className="flex items-center gap-6">
              <Link
                href="#"
                className="text-slate-400 hover:text-white text-sm transition-colors"
              >
                Privacy Policy
              </Link>
              <Link
                href="#"
                className="text-slate-400 hover:text-white text-sm transition-colors"
              >
                Disclaimer
              </Link>
            </div>
          </div>

          {/* Proactive Zone Link */}
          <div className="mt-8 pt-8 border-t border-primary-800 text-center">
            <Link
              href={siteConfig.links.proactiveZone}
              isExternal
              className="inline-flex items-center gap-2 text-slate-400 hover:text-white transition-colors"
            >
              <div className="w-8 h-8 bg-primary-700 rounded flex items-center justify-center">
                <span className="text-xs">PZ</span>
              </div>
              <span className="text-sm">Visit Proactive Zone</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Chat Widget Placeholder */}
      <div className="fixed bottom-6 right-6 z-50">
        <button className="bg-primary text-white p-4 rounded-full shadow-lg hover:bg-primary/90 transition-colors">
          <span className="text-lg">💬</span>
        </button>
      </div>
    </footer>
  );
};
