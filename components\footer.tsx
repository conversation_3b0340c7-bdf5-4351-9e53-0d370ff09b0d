"use client";
import { Link } from "@heroui/link";
import { siteConfig } from "@/config/site";
import { useLanguage } from "@/contexts/LanguageContext";
import { useState } from "react";

export const Footer = () => {
  const { t, isRTL } = useLanguage();
  const [chatOpen, setChatOpen] = useState(false);
  const [chatInput, setChatInput] = useState("");
  const [chatMessages, setChatMessages] = useState([
    { from: "bot", text: "Hi! How can I help you today?" },
  ]);

  const handleSend = () => {
    if (!chatInput.trim()) return;
    setChatMessages((msgs) => [
      ...msgs,
      { from: "user", text: chatInput },
      { from: "bot", text: `You said: ${chatInput}` }, // Simple echo response
    ]);
    setChatInput("");
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") handleSend();
  };
  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg">
                <span className="text-primary font-bold text-lg">P</span>
              </div>
              <div className="flex flex-col">
                <p className="font-bold text-white text-sm leading-tight">
                  Proactive
                </p>
                <p className="font-bold text-white text-sm leading-tight">
                  Wealth Management
                </p>
              </div>
            </div>
            <p className="text-slate-300 mb-6 max-w-md leading-relaxed">
              {t("footer.description")}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-white mb-4">
              {t("footer.quickLinks")}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.about")}
                </Link>
              </li>
              <li>
                <Link
                  href="/client-journey"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.clientJourney")}
                </Link>
              </li>
              <li>
                <Link
                  href="/insights"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.insights")}
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-slate-300 hover:text-white transition-colors"
                >
                  {t("nav.contact")}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-white mb-4">
              {t("footer.contact")}
            </h3>
            <div className="space-y-2 text-slate-300">
              <p>{siteConfig.contact.address}</p>
              <p>Phone: {siteConfig.contact.phone}</p>
              <p>Email: {siteConfig.contact.email}</p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-800 mt-12 pt-8 text-center">
          <div className="text-center w-full flex flex-col items-center justify-center">
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <p className="text-slate-400 text-sm">{t("footer.rights")}</p>
            </div>
            <div className="flex flex-row items-center gap-6 w-full text-center justify-center pt-2">
              <Link
                href="#"
                className="text-slate-400 hover:text-white text-sm transition-colors"
              >
                {t("footer.privacy")}
              </Link>
              <Link
                href="#"
                className="text-slate-400 hover:text-white text-sm transition-colors"
              >
                {t("footer.disclaimer")}
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Widget Placeholder - Left Bottom */}
      <div className="fixed bottom-6 left-6 z-50">
        <button
          className="bg-primary-900 text-white p-4 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
          onClick={() => setChatOpen((open) => !open)}
        >
          <span className="text-lg">💬</span>
        </button>
        {chatOpen && (
          <div
            className="absolute bottom-20 left-0 w-80 max-w-xs sm:max-w-sm bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col overflow-hidden animate-fadeIn transition-all duration-300 p-0 sm:p-0"
            style={{ minWidth: 280, maxWidth: "95vw", right: "auto" }}
          >
            <div className="flex items-center justify-between px-4 py-2 font-semibold rounded-t-2xl bg-gradient-to-r from-primary-900 to-primary-700 shadow">
              <span className="tracking-wide">Ask PWM</span>
              <button
                className="ml-2 p-1 rounded-full hover:bg-white/10 text-white text-xl focus:outline-none focus:ring-2 focus:ring-white/50 transition"
                onClick={() => setChatOpen(false)}
                aria-label="Close chat"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 6L14 14M14 6L6 14"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </button>
            </div>
            <div
              className="flex-1 p-4 space-y-2 overflow-y-auto max-h-60 text-sm bg-gray-50"
              style={{ minHeight: 120 }}
            >
              {chatMessages.map((msg, i) => (
                <div
                  key={i}
                  className={
                    msg.from === "user"
                      ? "flex justify-end"
                      : "flex justify-start"
                  }
                >
                  <span
                    className={
                      msg.from === "user"
                        ? "bg-primary-100 text-primary-900 px-4 py-2 rounded-2xl shadow-md inline-block max-w-full sm:max-w-[80%] break-words whitespace-pre-line"
                        : "bg-white border border-gray-200 text-gray-900 px-4 py-2 rounded-2xl shadow-sm inline-block max-w-full sm:max-w-[80%] break-words whitespace-pre-line"
                    }
                    style={{
                      wordBreak: "break-word",
                      overflowWrap: "break-word",
                    }}
                  >
                    {msg.text}
                  </span>
                </div>
              ))}
            </div>
            <div className="flex border-t border-gray-200 bg-white p-2 gap-2">
              <input
                className="flex-1 px-3 py-2 rounded-xl border border-gray-200 focus:ring-2 focus:ring-primary-400 outline-none text-gray-900 bg-gray-50 shadow-sm"
                type="text"
                placeholder="Type your message..."
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={handleInputKeyDown}
              />
              <button
                className="bg-gradient-to-r from-primary-900 to-primary-700 text-white px-4 py-2 rounded-xl font-semibold shadow hover:from-primary-700 hover:to-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-400 transition-all"
                onClick={handleSend}
              >
                Send
              </button>
            </div>
          </div>
        )}
      </div>
      {/* Image Widget - Right Bottom */}
      <Link
        href="/my-folder/index.html"
        className="floating-tile fixed bottom-6 animate-float right-6 z-40 block w-48 max-md:w-20 rounded-lg shadow-xl overflow-hidden opacity-80 hover:opacity-100 transition-all duration-300 ease-in-out transform hover:scale-105"
        // style={{ width: 120, height: 120 }}
      >
        <img
          src="/proactive.webp"
          alt="image"
          className="w-full h-auto block"
        />
      </Link>
    </footer>
  );
};
