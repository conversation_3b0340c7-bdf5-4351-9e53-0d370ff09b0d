"use client";
import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'EN' | 'AR';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: React.ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('EN');

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'EN' || savedLanguage === 'AR')) {
      setLanguageState(savedLanguage);
    }
  }, []);

  // Save language to localStorage when it changes
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
    
    // Update document direction
    document.documentElement.dir = lang === 'AR' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang === 'AR' ? 'ar' : 'en';
  };

  // Translation function
  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  const isRTL = language === 'AR';

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Translation data
const translations = {
  EN: {
    // Navigation
    'nav.about': 'About',
    'nav.clientJourney': 'Client Journey',
    'nav.insights': 'Insights',
    'nav.contact': 'Book a Consultation',
    
    // Hero Section
    'hero.title': 'Preserve More. Pay Less. Protect Everything.',
    'hero.subtitle': 'Your legal edge in global tax & trust planning.',
    'hero.cta': 'Book Your Private Consultation',
    
    // Expertise Section
    'expertise.title': 'Our Expertise',
    'expertise.subtitle': 'Comprehensive solutions for your wealth management needs',
    'expertise.tax.title': 'Tax Advisory',
    'expertise.tax.description': 'Navigate complex tax landscapes with confidence.',
    'expertise.company.title': 'Company Formation',
    'expertise.company.description': 'Establish your business presence in strategic jurisdictions.',
    'expertise.trust.title': 'Trust Formation',
    'expertise.trust.description': 'Protect and preserve your wealth for future generations.',
    
    // Why PWM Section
    'whyPwm.title': 'Why PWM?',
    'whyPwm.subtitle': 'Your trusted partner in wealth optimization',
    'whyPwm.expertise.title': 'Proven Expertise',
    'whyPwm.expertise.description': 'Over a decade of experience in Dubai\'s financial landscape',
    'whyPwm.global.title': 'Global Reach',
    'whyPwm.global.description': 'International network spanning multiple jurisdictions',
    'whyPwm.efficiency.title': 'Time Efficiency',
    'whyPwm.efficiency.description': 'Streamlined processes that save you valuable time',
    
    // Testimonial
    'testimonial.quote': 'Proactivezone transformed our expansion into Dubai from a daunting challenge into a streamlined process. Their expertise saved us significant resources.',
    'testimonial.author': 'Sarah Johnson',
    'testimonial.position': 'CEO, Global Ventures',
    
    // Client Journey
    'clientJourney.title': 'Client Journey',
    'clientJourney.subtitle': 'Our proven process for success',
    'clientJourney.cta': 'Start Your Journey',
    
    // Insights
    'insights.title': 'Insights',
    'insights.subtitle': 'Stay informed with our latest perspectives',
    'insights.viewAll': 'View All Insights',
    'insights.readMore': 'Read More',
    
    // CTA Section
    'cta.title': 'Ready to Optimize Your Wealth Strategy?',
    'cta.subtitle': 'Let\'s discuss how we can help you achieve your financial goals.',
    'cta.button': 'Get Started Today',
    
    // Footer
    'footer.description': 'Your trusted partner in global wealth management and tax optimization.',
    'footer.quickLinks': 'Quick Links',
    'footer.services': 'Services',
    'footer.contact': 'Contact Info',
    'footer.rights': '© 2024 Proactive Wealth Management. All Rights Reserved.',
    'footer.privacy': 'Privacy Policy',
    'footer.disclaimer': 'Disclaimer',
    'footer.visitProactiveZone': 'Visit Proactive Zone',
  },
  AR: {
    // Navigation
    'nav.about': 'حولنا',
    'nav.clientJourney': 'رحلة العميل',
    'nav.insights': 'الرؤى',
    'nav.contact': 'احجز استشارة',
    
    // Hero Section
    'hero.title': 'احفظ أكثر. ادفع أقل. احم كل شيء.',
    'hero.subtitle': 'ميزتك القانونية في التخطيط الضريبي والائتماني العالمي.',
    'hero.cta': 'احجز استشارتك الخاصة',
    
    // Expertise Section
    'expertise.title': 'خبرتنا',
    'expertise.subtitle': 'حلول شاملة لاحتياجات إدارة ثروتك',
    'expertise.tax.title': 'الاستشارات الضريبية',
    'expertise.tax.description': 'تنقل في المشاهد الضريبية المعقدة بثقة.',
    'expertise.company.title': 'تأسيس الشركات',
    'expertise.company.description': 'أسس حضور عملك في الولايات القضائية الاستراتيجية.',
    'expertise.trust.title': 'تكوين الصناديق الائتمانية',
    'expertise.trust.description': 'احم واحفظ ثروتك للأجيال القادمة.',
    
    // Why PWM Section
    'whyPwm.title': 'لماذا PWM؟',
    'whyPwm.subtitle': 'شريكك الموثوق في تحسين الثروة',
    'whyPwm.expertise.title': 'خبرة مثبتة',
    'whyPwm.expertise.description': 'أكثر من عقد من الخبرة في المشهد المالي لدبي',
    'whyPwm.global.title': 'الوصول العالمي',
    'whyPwm.global.description': 'شبكة دولية تمتد عبر ولايات قضائية متعددة',
    'whyPwm.efficiency.title': 'كفاءة الوقت',
    'whyPwm.efficiency.description': 'عمليات مبسطة توفر لك وقتاً ثميناً',
    
    // Testimonial
    'testimonial.quote': 'حولت بروأكتيف زون توسعنا إلى دبي من تحدٍ مخيف إلى عملية مبسطة. خبرتهم وفرت لنا موارد كبيرة.',
    'testimonial.author': 'سارة جونسون',
    'testimonial.position': 'الرئيس التنفيذي، المشاريع العالمية',
    
    // Client Journey
    'clientJourney.title': 'رحلة العميل',
    'clientJourney.subtitle': 'عمليتنا المثبتة للنجاح',
    'clientJourney.cta': 'ابدأ رحلتك',
    
    // Insights
    'insights.title': 'الرؤى',
    'insights.subtitle': 'ابق على اطلاع بأحدث وجهات نظرنا',
    'insights.viewAll': 'عرض جميع الرؤى',
    'insights.readMore': 'اقرأ المزيد',
    
    // CTA Section
    'cta.title': 'هل أنت مستعد لتحسين استراتيجية ثروتك؟',
    'cta.subtitle': 'دعنا نناقش كيف يمكننا مساعدتك في تحقيق أهدافك المالية.',
    'cta.button': 'ابدأ اليوم',
    
    // Footer
    'footer.description': 'شريكك الموثوق في إدارة الثروات العالمية وتحسين الضرائب.',
    'footer.quickLinks': 'روابط سريعة',
    'footer.services': 'الخدمات',
    'footer.contact': 'معلومات الاتصال',
    'footer.rights': '© 2024 إدارة الثروات الاستباقية. جميع الحقوق محفوظة.',
    'footer.privacy': 'سياسة الخصوصية',
    'footer.disclaimer': 'إخلاء المسؤولية',
    'footer.visitProactiveZone': 'زيارة المنطقة الاستباقية',
  }
};
