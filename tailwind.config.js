import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
    "./screens/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        primary: {
          DEFAULT: "#1d4930",
          50: "#f0f9f4",
          100: "#dcf2e4",
          200: "#bce5cd",
          300: "#8dd1ab",
          400: "#56b482",
          500: "#339966",
          600: "#1d4930",
          700: "#1a3f2a",
          800: "#173424",
          900: "#142b1f",
          950: "#0a1610",
          foreground: "#ffffff",
        },
        secondary: {
          DEFAULT: "#c9a338",
          50: "#fefbf3",
          100: "#fdf6e3",
          200: "#faecc2",
          300: "#f6dd96",
          400: "#f0c968",
          500: "#eab543",
          600: "#c9a338",
          700: "#b8912f",
          800: "#96742b",
          900: "#7a5f28",
          950: "#453513",
          foreground: "#ffffff",
        },
      },
    },
  },
  darkMode: "class",
  plugins: [heroui()],
};

module.exports = config;
