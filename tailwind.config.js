import {heroui} from "@heroui/theme"

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
    },
  },
   colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        brand: {
          DEFAULT: "#1E1E76",
          white: "#FFFFFF",
          black: "#080236",
          grey: "#8C8C8C",
          border: "#EBEBEB",
        },

        accent: {
          DEFAULT: "#f2f2ff",
          foreground: "#707ff5",
        },

        neutral: {
          DEFAULT: "#EBEBEB",
          dark: "#999999",
          foreground: "#8C8C8C",
        },

        // Functional Colors
        success: {
          DEFAULT: "#84ebb4", // Green
          dark: "#1fc16b",
          foreground: "#1fc16b",
        },

        warning: {
          DEFAULT: "#f7c83b", // Yellow/Orange
          dark: "#e4ae0d",
          foreground: "#f7c83b",
        },

        error: {
          DEFAULT: "#fb3748", // Red
          dark: "#d00416",
          foreground: "#fb3748",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },

        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        "lucky-blue": "#1E1E76",
        portage: "#A195F9",
        cornflower: "#707FF5",
      },
  darkMode: "class",
  plugins: [heroui()],
}

module.exports = config;