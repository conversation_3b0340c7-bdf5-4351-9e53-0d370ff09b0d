"use client";
import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";
import { useLanguage } from "@/contexts/LanguageContext";

export default function Home() {
  const { t, isRTL } = useLanguage();
  return (
    <div className={`bg-white ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Mobile Background Image with Overlay */}
        <div className="lg:hidden absolute inset-0 z-0">
          <img
            src="/dubaiskyline.webp"
            alt="Dubai Skyline"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gray-900/60"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div
              className={`text-center lg:text-left text-primary font-serif ${isRTL ? "lg:text-right pl-10" : "pr-10"}`}
            >
              <h1 className="text-5xl lg:text-7xl font-medium leading-tight mb-6 text-primary max-md:text-white">
                {t("hero.title")}
              </h1>
              <p className="text-xl text-gray-200 lg:text-gray-600 mb-8 max-w-2xl">
                {t("hero.subtitle")}
              </p>
              <Button
                as={Link}
                href="#contact"
                className="bg-secondary text-white rounded-md px-8 py-3 text-lg font-medium hover:bg-secondary/90 transition-colors shadow-lg"
                size="lg"
              >
                {t("hero.cta")}
              </Button>
            </div>
            <div className="relative hidden lg:block">
              <div className="w-full h-96 rounded-lg overflow-hidden shadow-2xl">
                <img
                  src="/dubaiskyline.webp"
                  alt="Dubai Skyline"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Expertise Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`text-center mb-16 ${isRTL ? "text-right" : "text-left"}`}
          >
            <h2 className="text-3xl lg:text-4xl font-medium  mb-4 text-primary font-serif">
              {t("expertise.title")}
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              {t("expertise.subtitle")}
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12  rounded-full flex items-center justify-center mx-auto mb-6">
                <svg
                  className="h-8 w-8 text-emerald-green"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                {t("expertise.tax.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("expertise.tax.description")}
              </p>
            </div>
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 bg-gradient-to-br  rounded-full flex items-center justify-center mx-auto mb-6">
                <svg
                  className="h-8 w-8 text-emerald-green"
                  fill="none"
                  height="24"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16" />
                  <rect height="10" rx="2" width="20" x="2" y="10" />
                  <path d="M6 10V4" />
                  <path d="M18 10V4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                {t("expertise.company.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("expertise.company.description")}
              </p>
            </div>
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg
                  className="h-8 w-8 text-emerald-green"
                  fill="none"
                  height="24"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  viewBox="0 0 24 24"
                  width="24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M21.2 15c.7-1.2 1-2.5.7-3.9-.6-2.4-3.4-4.1-6.1-4.1H8.8c-2.5 0-4.9 1.4-5.8 3.5-.9 2.1-.4 4.6 1.1 6.3" />
                  <path d="M11.4 15.2a4 4 0 0 1 0-6.4" />
                  <path d="M12.8 13.8a4 4 0 0 1 0-6.4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                {t("expertise.trust.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("expertise.trust.description")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why PWM Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="w-full h-96 rounded-xl overflow-hidden shadow-2xl">
                <img
                  src="/clienttrust.webp"
                  alt="Client Trust and Dubai Skyline"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
            <div>
              <h2 className="text-3xl lg:text-4xl font-medium  mb-8 text-primary font-serif">
                {t("whyPwm.title")}
              </h2>
              <p className="text-xl text-slate-600 mb-8">
                {t("whyPwm.subtitle")}
              </p>
              <div className="space-y-8">
                <div
                  className={`flex items-start ${isRTL ? "space-x-reverse" : ""} space-x-4`}
                >
                  <div className="w-6 h-6  mt-0 text-secondary">
                    <svg
                      className="w-6 h-6 text-gold mr-3 flex-shrink-0 mt-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 12l2 2 4-4m6-4a9 9 0 11-18 0 9 9 0 0118 0z"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-3">
                      {t("whyPwm.expertise.title")}
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      {t("whyPwm.expertise.description")}
                    </p>
                  </div>
                </div>
                <div
                  className={`flex items-start ${isRTL ? "space-x-reverse" : ""} space-x-4`}
                >
                  <div className="w-6 h-6  mt-0 text-secondary">
                    <svg
                      className="w-6 h-6 text-gold mr-3 flex-shrink-0 mt-1 text-secondary"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9V3m-9 9h18"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-3">
                      {t("whyPwm.global.title")}
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      {t("whyPwm.global.description")}
                    </p>
                  </div>
                </div>
                <div
                  className={`flex items-start ${isRTL ? "space-x-reverse" : ""} space-x-4`}
                >
                  <div className="w-6 h-6  mt-0 text-secondary">
                    <svg
                      className="w-6 h-6 text-gold mr-3 flex-shrink-0 mt-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-3">
                      {t("whyPwm.efficiency.title")}
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      {t("whyPwm.efficiency.description")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="pt-6 pb-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-8xl mx-auto text-center">
            <div className="bg-white p-12 rounded-2xl">
              <div className="text-6xl text-primary mb-6">&ldquo;</div>
              <blockquote className="text-xl lg:text-2xl text-slate-700 italic mb-8 leading-relaxed">
                {t("testimonial.quote")}
              </blockquote>
              <div className="text-slate-600">
                <p className="font-semibold text-slate-900 text-lg">
                  - {t("testimonial.author")}, {t("testimonial.position")}
                </p>
                <p className="text-slate-500">International Trading LLC</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-primary-700">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-medium text-white mb-4 font-serif">
            {t("cta.title")}
          </h2>
          <p className="text-xl text-primary-100 mb-8">{t("cta.subtitle")}</p>
          <Button
            as={Link}
            href="#contact"
            className="bg-secondary rounded-md text-white px-8 py-3 text-lg font-semibold hover:bg-secondary/90 transition-colors shadow-lg"
            size="lg"
          >
            {t("cta.button")}
          </Button>
        </div>
      </section>

      {/* Client Journey Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-medium text-primary mb-4 font-serif">
              {t("clientJourney.title")}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              {t("clientJourney.subtitle")}
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                1
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                {t("journey.step1.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("journey.step1.description")}
              </p>
            </div>
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                2
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                {t("journey.step2.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("journey.step2.description")}
              </p>
            </div>
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                3
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                {t("journey.step3.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("journey.step3.description")}
              </p>
            </div>
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                4
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                {t("journey.step4.title")}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {t("journey.step4.description")}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Insights Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-medium font-serif text-primary mb-4">
              {t("insights.title")}
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              {t("insights.subtitle")}
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <article className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#1d4930]/20">
              <div className="h-48 bg-gradient-to-br from-[#1d4930]/10 to-[#2d5940]/10"></div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-xs bg-[#1d4930]/10 text-[#1d4930] px-2 py-1 rounded-full font-medium">
                    {t("insights.categories.tax")}
                  </span>
                  <span className="text-xs text-gray-600">
                    5 {t("common.minRead")}
                  </span>
                </div>
                <h3 className="text-xl font-semibold mb-3 hover:text-[#1d4930] cursor-pointer text-gray-900">
                  {t("insights.article1.title")}
                </h3>
                <p className="text-gray-700 mb-4 line-clamp-3">
                  {t("insights.article1.description")}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">April 18, 2024</span>
                  <button className="text-[#1d4930] hover:text-[#1d4930]/80 font-medium text-sm">
                    {t("common.readMore")} →
                  </button>
                </div>
              </div>
            </article>

            <article className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#1d4930]/20">
              <div className="h-48 bg-gradient-to-br from-[#1d4930]/10 to-[#2d5940]/10"></div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-xs bg-[#1d4930]/10 text-[#1d4930] px-2 py-1 rounded-full font-medium">
                    {t("insights.categories.trust")}
                  </span>
                  <span className="text-xs text-gray-600">
                    7 {t("common.minRead")}
                  </span>
                </div>
                <h3 className="text-xl font-semibold mb-3 hover:text-[#1d4930] cursor-pointer text-gray-900">
                  {t("insights.article2.title")}
                </h3>
                <p className="text-gray-700 mb-4 line-clamp-3">
                  {t("insights.article2.description")}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">March 28, 2024</span>
                  <button className="text-[#1d4930] hover:text-[#1d4930]/80 font-medium text-sm">
                    {t("common.readMore")} →
                  </button>
                </div>
              </div>
            </article>

            <article className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-[#1d4930]/20">
              <div className="h-48 bg-gradient-to-br from-[#1d4930]/10 to-[#2d5940]/10"></div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-xs bg-[#1d4930]/10 text-[#1d4930] px-2 py-1 rounded-full font-medium">
                    {t("insights.categories.business")}
                  </span>
                  <span className="text-xs text-gray-600">
                    6 {t("common.minRead")}
                  </span>
                </div>
                <h3 className="text-xl font-semibold mb-3 hover:text-[#1d4930] cursor-pointer text-gray-900">
                  {t("insights.article3.title")}
                </h3>
                <p className="text-gray-700 mb-4 line-clamp-3">
                  {t("insights.article3.description")}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    February 10, 2024
                  </span>
                  <button className="text-[#1d4930] hover:text-[#1d4930]/80 font-medium text-sm">
                    {t("common.readMore")} →
                  </button>
                </div>
              </div>
            </article>
          </div>
          <div className="text-center">
            <Button
              as={Link}
              href="/insights"
              variant="flat"
              className="border-primary text-white hover:bg-primary/90 bg-primary px-8 py-4 rounded-md"
            >
              {t("insights.viewAll")}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
