import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";

export default function Home() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-20 lg:py-32 text-white">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight mb-6">
                Preserve More. Pay Less. Protect Everything.
              </h1>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl">
                Your legal edge in global tax & trust planning.
              </p>
              <Button
                as={Link}
                href="#contact"
                className="bg-blue-600 text-white px-8 py-3 text-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg"
                size="lg"
              >
                Book Your Private Consultation
              </Button>
            </div>
            <div className="relative">
              <div className="w-full h-96 bg-gradient-to-r from-slate-700 to-slate-600 rounded-lg flex items-center justify-center shadow-2xl">
                <span className="text-gray-300 text-lg">Dubai Skyline</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Expertise Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              Our Expertise
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">📊</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                Tax Advisory
              </h3>
              <p className="text-slate-600 leading-relaxed">
                Navigate complex tax landscapes with confidence.
              </p>
            </div>
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">🏢</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                Company Formation
              </h3>
              <p className="text-slate-600 leading-relaxed">
                Establish your business with ease in Dubai.
              </p>
            </div>
            <div className="text-center p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                Trust Formation
              </h3>
              <p className="text-slate-600 leading-relaxed">
                Create trusts to protect your assets and legacy.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why PWM Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="w-full h-96 bg-gradient-to-r from-slate-600 to-slate-700 rounded-xl flex items-center justify-center shadow-2xl">
                <span className="text-slate-300 text-lg">
                  Client Trust & Skyline
                </span>
              </div>
            </div>
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-8">
                Why PWM?
              </h2>
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-3">
                      Discreet Advisory
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      Your privacy is paramount. We provide confidential,
                      bespoke advice tailored to your unique circumstances.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-3">
                      Global Understanding
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      Our team possesses deep international expertise to
                      navigate complex cross-border regulations.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-3 flex-shrink-0"></div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-3">
                      Legacy Planning
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      We help you build and protect a lasting legacy for future
                      generations.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white p-12 rounded-2xl shadow-xl">
              <div className="text-6xl text-blue-600 mb-6">"</div>
              <blockquote className="text-xl lg:text-2xl text-slate-700 italic mb-8 leading-relaxed">
                Proactivezone transformed our expansion into Dubai from a
                daunting challenge into a streamlined process. Their expertise
                saved us significant resources.
              </blockquote>
              <div className="text-slate-600">
                <p className="font-semibold text-slate-900 text-lg">
                  - Sarah Johnson, CFO
                </p>
                <p className="text-slate-500">International Trading LLC</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Client Journey Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              Our Client Journey
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              A streamlined, four-step process to structure and protect your
              wealth.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                1
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                Consultation
              </h3>
              <p className="text-slate-600 leading-relaxed">
                We analyze your needs and develop a strategic plan.
              </p>
            </div>
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                2
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                Tax Structure
              </h3>
              <p className="text-slate-600 leading-relaxed">
                We design the optimal, tax-efficient structure for you.
              </p>
            </div>
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                3
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                Legal Setup
              </h3>
              <p className="text-slate-600 leading-relaxed">
                Our experts handle all legal and financial requirements.
              </p>
            </div>
            <div className="text-center p-6 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-6 text-xl font-bold shadow-lg">
                4
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                Ongoing Advisory
              </h3>
              <p className="text-slate-600 leading-relaxed">
                We continue to optimize and ensure long-term success.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Insights Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              Insights
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <article className="bg-white rounded-xl p-8 hover:shadow-lg transition-shadow">
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                Tax-Efficient Investing in Dubai
              </h3>
              <p className="text-slate-500 text-sm mb-4">April 18, 2024</p>
            </article>
            <article className="bg-white rounded-xl p-8 hover:shadow-lg transition-shadow">
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                The Benefits of Trusts in Dubai
              </h3>
              <p className="text-slate-500 text-sm mb-4">March 28, 2024</p>
            </article>
            <article className="bg-white rounded-xl p-8 hover:shadow-lg transition-shadow">
              <h3 className="text-xl font-semibold text-slate-900 mb-4">
                Why Entrepreneurs Are Moving to Dubai
              </h3>
              <p className="text-slate-500 text-sm mb-4">February 10, 2024</p>
            </article>
          </div>
          <div className="text-center">
            <Button
              as={Link}
              href="/insights"
              variant="bordered"
              className="border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3"
            >
              See All Insights
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Structure Your Wealth Smarter?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Speak with a PWM advisor today.
          </p>
          <Button
            as={Link}
            href="#contact"
            className="bg-white text-blue-600 px-8 py-3 text-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg"
            size="lg"
          >
            Book a Call
          </Button>
        </div>
      </section>
    </div>
  );
}
