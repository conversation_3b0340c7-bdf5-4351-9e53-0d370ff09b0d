import { But<PERSON> } from "@heroui/button";
import { Link } from "@heroui/link";

export default function Home() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-white py-20 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                Preserve More. Pay Less. Protect Everything.
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-2xl">
                Your legal edge in global tax & trust planning.
              </p>
              <Button
                as={Link}
                href="#contact"
                className="bg-blue-600 text-white px-8 py-3 text-lg font-semibold hover:bg-blue-700 transition-colors"
                size="lg"
              >
                Book Your Private Consultation
              </Button>
            </div>
            <div className="relative">
              <div className="w-full h-96 bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-500 text-lg">
                  Dubai Skyline Image
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Expertise Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Expertise
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 text-2xl">📊</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Tax Advisory
              </h3>
              <p className="text-gray-600">
                Navigate complex tax landscapes with confidence.
              </p>
            </div>
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 text-2xl">🏢</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Company Formation
              </h3>
              <p className="text-gray-600">
                Establish your business with ease in Dubai.
              </p>
            </div>
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 text-2xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Trust Formation
              </h3>
              <p className="text-gray-600">
                Create trusts to protect your assets and legacy.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why PWM Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="w-full h-96 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg flex items-center justify-center">
                <span className="text-gray-500 text-lg">
                  Client Trust Image
                </span>
              </div>
            </div>
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
                Why PWM?
              </h2>
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    Discreet Advisory
                  </h3>
                  <p className="text-gray-600">
                    Your privacy is paramount. We provide confidential, bespoke
                    advice tailored to your unique circumstances.
                  </p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    Global Understanding
                  </h3>
                  <p className="text-gray-600">
                    Our team possesses deep international expertise to navigate
                    complex cross-border regulations.
                  </p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    Legacy Planning
                  </h3>
                  <p className="text-gray-600">
                    We help you build and protect a lasting legacy for future
                    generations.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <blockquote className="text-xl lg:text-2xl text-gray-700 italic mb-8">
              "Proactivezone transformed our expansion into Dubai from a
              daunting challenge into a streamlined process. Their expertise
              saved us significant resources."
            </blockquote>
            <div className="text-gray-600">
              <p className="font-semibold">- Sarah Johnson, CFO</p>
              <p>International Trading LLC</p>
            </div>
          </div>
        </div>
      </section>

      {/* Client Journey Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Client Journey
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A streamlined, four-step process to structure and protect your
              wealth.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Consultation
              </h3>
              <p className="text-gray-600">
                We analyze your needs and develop a strategic plan.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Tax Structure
              </h3>
              <p className="text-gray-600">
                We design the optimal, tax-efficient structure for you.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Legal Setup
              </h3>
              <p className="text-gray-600">
                Our experts handle all legal and financial requirements.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Ongoing Advisory
              </h3>
              <p className="text-gray-600">
                We continue to optimize and ensure long-term success.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Insights Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Insights
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <article className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Tax-Efficient Investing in Dubai
              </h3>
              <p className="text-gray-600 text-sm mb-4">April 18, 2024</p>
            </article>
            <article className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                The Benefits of Trusts in Dubai
              </h3>
              <p className="text-gray-600 text-sm mb-4">March 28, 2024</p>
            </article>
            <article className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Why Entrepreneurs Are Moving to Dubai
              </h3>
              <p className="text-gray-600 text-sm mb-4">February 10, 2024</p>
            </article>
          </div>
          <div className="text-center">
            <Button
              as={Link}
              href="/insights"
              variant="bordered"
              className="border-blue-600 text-blue-600 hover:bg-blue-50"
            >
              See All Insights
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            Ready to Structure Your Wealth Smarter?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Speak with a PWM advisor today.
          </p>
          <Button
            as={Link}
            href="#contact"
            className="bg-white text-blue-600 px-8 py-3 text-lg font-semibold hover:bg-gray-100 transition-colors"
            size="lg"
          >
            Book a Call
          </Button>
        </div>
      </section>
    </div>
  );
}
