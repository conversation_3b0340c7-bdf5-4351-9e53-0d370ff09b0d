import React from "react";

interface AboutScreenProps {
  // Add any props you need here
}

const AboutScreen: React.FC<AboutScreenProps> = () => {
  return (
    <div className="bg-white min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-8 text-gray-900">
            About Us
          </h1>

          <div className="prose prose-lg mx-auto">
            <p className="text-lg text-gray-700 mb-6">
              Welcome to Proactive Wealth Management. We are your trusted partner in global tax & trust planning, 
              helping you preserve more, pay less, and protect everything that matters to you.
            </p>

            <div className="grid md:grid-cols-2 gap-8 mt-12">
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <h2 className="text-2xl font-semibold mb-4 text-gray-900">
                  Our Mission
                </h2>
                <p className="text-gray-700">
                  To provide exceptional wealth management and tax planning services that help our clients 
                  achieve their financial goals while ensuring compliance with international regulations.
                </p>
              </div>

              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <h2 className="text-2xl font-semibold mb-4 text-gray-900">
                  Our Vision
                </h2>
                <p className="text-gray-700">
                  To be the leading wealth management firm in the UAE, known for our expertise, 
                  integrity, and commitment to client success.
                </p>
              </div>
            </div>

            <div className="mt-12">
              <h2 className="text-3xl font-semibold mb-6 text-gray-900">
                Our Expertise
              </h2>
              <p className="text-gray-700 mb-8">
                With years of experience in international tax planning and wealth management, 
                our team provides comprehensive solutions tailored to your unique needs.
              </p>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center bg-gray-50 p-6 rounded-lg border border-gray-200">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-blue-600 text-2xl">📊</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Tax Advisory</h3>
                  <p className="text-gray-600 text-sm">Expert guidance on complex tax matters</p>
                </div>

                <div className="text-center bg-gray-50 p-6 rounded-lg border border-gray-200">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-blue-600 text-2xl">🏢</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Company Formation</h3>
                  <p className="text-gray-600 text-sm">Seamless business setup in Dubai</p>
                </div>

                <div className="text-center bg-gray-50 p-6 rounded-lg border border-gray-200">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-blue-600 text-2xl">🛡️</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Trust Formation</h3>
                  <p className="text-gray-600 text-sm">Asset protection and legacy planning</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutScreen;
