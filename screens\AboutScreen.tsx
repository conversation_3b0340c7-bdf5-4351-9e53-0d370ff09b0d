"use client";
import React from "react";

interface AboutScreenProps {
  // Add any props you need here
}

const AboutScreen: React.FC<AboutScreenProps> = () => {
  return (
    <div className="bg-gradient-to-b from-[#e9f5ec] to-white min-h-screen text-gray-800 font-sans">
      {/* Hero Section */}
      <header className="text-center py-20 px-4">
        <h1 className="text-4xl md:text-5xl font-bold leading-snug">
          Building Business Success in{" "}
          <span className="text-green-700">Dubai Since</span>
        </h1>
        <p className="text-green-700 text-4xl md:text-5xl font-extrabold mt-4">
          2014
        </p>
      </header>

      {/* History & Mission Section */}
      <section className="max-w-6xl mx-auto px-4 md:px-8 py-20 grid md:grid-cols-2 gap-8">
        <div className="bg-white shadow-md rounded-2xl p-6 border-t-4 border-green-600">
          <h2 className="text-2xl font-semibold text-green-700 border-b border-green-500 pb-2 mb-4">
            Our History
          </h2>
          <p className="text-gray-700">
            Established in 2014, Proactivezone emerged from a vision to simplify
            the complex process of establishing and operating businesses in
            Dubai. What began as a specialized tax advisory firm has evolved
            into a comprehensive business solutions provider, guiding hundreds
            of companies.
          </p>
        </div>

        <div className="bg-white shadow-md rounded-2xl p-6 border-t-4 border-green-600">
          <h2 className="text-2xl font-semibold text-green-700 border-b border-green-500 pb-2 mb-4">
            Our Mission
          </h2>
          <p className="text-gray-700">
            At Proactivezone, our mission is to empower businesses and
            individuals to thrive in Dubai’s competitive marketplace by
            providing expert guidance, transparent solutions, and personalized
            support throughout their business journey.
          </p>
        </div>
      </section>

      {/* Approach & Values */}
      <section className="bg-white py-24 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-4">
            Our Approach & <span className="text-green-700">Values</span>
          </h2>
          <p className="text-gray-600 mb-12 text-lg">
            Our client-centric approach is built on a foundation of core values
            that drive everything we do.
          </p>
          <div className="grid md:grid-cols-3 gap-8 text-left">
            {[
              {
                title: "Integrity",
                desc: "We operate with complete transparency and ethical standards, ensuring our clients receive honest advice and solutions that prioritize their long-term interests.",
              },
              {
                title: "Excellence",
                desc: "We pursue excellence in every aspect of our service, combining technical expertise with attention to detail to deliver solutions that exceed expectations.",
              },
              {
                title: "Proactivity",
                desc: "True to our name, we take a proactive approach to anticipating challenges and identifying opportunities, keeping clients ahead of regulatory changes and market developments.",
              },
              {
                title: "Collaboration",
                desc: "We value collaboration, both within our team and with our clients, fostering trust, communication, and long-lasting partnerships.",
              },
              {
                title: "Innovation",
                desc: "We continuously adapt and innovate to stay ahead in a dynamic market, offering our clients fresh and effective business strategies.",
              },
              {
                title: "Accountability",
                desc: "We take full responsibility for our services and actions, ensuring reliability and trust in all our client relationships.",
              },
            ].map((item, index) => (
              <div
                key={index}
                className="bg-[#f9fdf9] shadow-md rounded-2xl p-6 border-l-4 border-green-500"
              >
                <h3 className="text-xl font-semibold text-green-700 mb-3">
                  {item.title}
                </h3>
                <p className="text-gray-700 text-sm">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Section */}
      <section className="bg-white py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-2">
            Meet Our <span className="text-green-700">Leadership</span>
          </h2>
          <p className="text-gray-600 mb-10">
            Our team of seasoned professionals is dedicated to your success.
          </p>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Johnathan Doe",
                title: "Founder & CEO",
                desc: "With over 20 years of experience in international business structuring, Johnathan leads our team with a vision for excellence and client-centric service.",
                img: "https://randomuser.me/api/portraits/men/32.jpg",
              },
              {
                name: "Jane Smith",
                title: "Head of Tax Advisory",
                desc: "A certified tax advisor, Jane specializes in UAE Corporate Tax and VAT law, helping clients navigate the fiscal landscape with unparalleled expertise.",
                img: "https://randomuser.me/api/portraits/women/44.jpg",
              },
              {
                name: "Ahmed Al Falahi",
                title: "Head of Corporate Services",
                desc: "Ahmed manages all company formation and PRO services, leveraging his extensive network and deep understanding of government processes.",
                img: "https://randomuser.me/api/portraits/men/65.jpg",
              },
            ].map((member, i) => (
              <div
                key={i}
                className="bg-[#f9fdf9] rounded-2xl shadow-md p-6 text-center border-t-4 border-green-600"
              >
                <img
                  src={member.img}
                  alt={member.name}
                  className="w-24 h-24 mx-auto rounded-full border-4 border-green-500 object-cover mb-4"
                />
                <h3 className="text-xl font-bold text-green-700">
                  {member.name}
                </h3>
                <p className="font-medium text-sm text-green-800">
                  {member.title}
                </p>
                <p className="text-gray-700 text-sm mt-2">{member.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partners Section */}
      <section className="bg-white py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-2">
            Partners & <span className="text-green-700">Affiliations</span>
          </h2>
          <p className="text-gray-600 mb-10">
            Our qualifications and affiliations are a testament to our
            commitment to quality and professional excellence.
          </p>
          <div className="flex flex-wrap justify-center gap-8">
            {[...Array(1)].map((_, index) => (
              <div key={index} className="flex flex-wrap justify-center gap-8">
                <img
                  src="https://www.shutterstock.com/image-vector/dmc-logo-design-inspiration-unique-260nw-2341733259.jpg"
                  alt="DMCC Logo"
                  className="w-24 h-12 anim-target anim-zoom is-visible"
                  style={{ transitionDelay: "0.3s" }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src =
                      "https://placehold.co/100x50/ffffff/000000?text=DMCC";
                  }}
                />
                <img
                  src="https://img.freepik.com/free-vector/tax-service-branding-identity-corporate-vector-logo-bundle-design_460848-13825.jpg"
                  alt="Tax Agency Logo"
                  className="w-24 h-12 anim-target anim-zoom is-visible"
                  style={{ transitionDelay: "0.4s" }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src =
                      "https://placehold.co/100x50/ffffff/000000?text=Tax+Agency";
                  }}
                />
                <img
                  src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTkLgxrjM9v9bkkpTIV1-frtBtisrkmXI6ZLg&s"
                  alt="ISO 9001 Logo"
                  className="w-24 h-12 anim-target anim-zoom is-visible"
                  style={{ transitionDelay: "0.5s" }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src =
                      "https://placehold.co/100x50/ffffff/000000?text=ISO";
                  }}
                />
                <img
                  src="https://t3.ftcdn.net/jpg/04/87/32/52/360_F_487325299_f7EJDRgzgfjLBfiM0bxmXYkx3zFVPz8N.jpg"
                  alt="STEP Logo"
                  className="w-24 h-12 anim-target anim-zoom is-visible"
                  style={{ transitionDelay: "0.6s" }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src =
                      "https://placehold.co/100x50/ffffff/000000?text=STEP";
                  }}
                />
                <img
                  src="https://images.seeklogo.com/logo-png/61/2/dubai-economy-and-tourism-logo-png_seeklogo-613279.png"
                  alt="Dubai Tourism Logo"
                  className="w-24 h-12 anim-target anim-zoom is-visible"
                  style={{ transitionDelay: "0.7s" }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src =
                      "https://placehold.co/100x50/ffffff/000000?text=Dubai";
                  }}
                />
                <img
                  src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQlnMJnGCijlyExJLiw45MX-HYOYAogpSydAw&s"
                  alt="ACCA Logo"
                  className="w-24 h-12 anim-target anim-zoom is-visible"
                  style={{ transitionDelay: "0.8s" }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src =
                      "https://placehold.co/100x50/ffffff/000000?text=ACCA";
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial */}
      <section className="py-20 bg-[#e9f5ec] px-4">
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-xl italic text-gray-800 leading-relaxed">
            “Proactivezone turned our expansion into Dubai from a daunting
            challenge into a seamless experience. Their insights and efficiency
            saved us significant resources.”
          </p>
          <p className="mt-4 font-semibold text-green-900">
            - Sarah Johnson, CFO, International Trading
          </p>
        </div>
      </section>
    </div>
  );
};

export default AboutScreen;
