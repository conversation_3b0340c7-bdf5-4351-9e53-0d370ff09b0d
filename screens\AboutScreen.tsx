import React from 'react';

interface AboutScreenProps {
  // Add any props you need here
}

const AboutScreen: React.FC<AboutScreenProps> = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">About Us</h1>
        
        <div className="prose prose-lg mx-auto">
          <p className="text-lg text-gray-600 mb-6">
            Welcome to our about page. This is where you can share your company's story,
            mission, and values with your visitors.
          </p>
          
          <div className="grid md:grid-cols-2 gap-8 mt-12">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
              <p className="text-gray-600">
                Add your mission statement here. Describe what drives your organization
                and what you aim to achieve.
              </p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-semibold mb-4">Our Vision</h2>
              <p className="text-gray-600">
                Share your vision for the future. What do you hope to accomplish
                and how do you see your organization evolving?
              </p>
            </div>
          </div>
          
          <div className="mt-12">
            <h2 className="text-3xl font-semibold mb-6">Our Team</h2>
            <p className="text-gray-600 mb-8">
              Meet the people behind our success. Our dedicated team works tirelessly
              to deliver exceptional results.
            </p>
            
            {/* Add team member cards here */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <h3 className="font-semibold">Team Member 1</h3>
                <p className="text-gray-600">Position</p>
              </div>
              
              <div className="text-center">
                <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <h3 className="font-semibold">Team Member 2</h3>
                <p className="text-gray-600">Position</p>
              </div>
              
              <div className="text-center">
                <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <h3 className="font-semibold">Team Member 3</h3>
                <p className="text-gray-600">Position</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutScreen;
